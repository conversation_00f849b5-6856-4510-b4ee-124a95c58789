# Quick Start Guide - CUFE Login System

## 🚨 Current Issue Resolution

You're getting a database connection error because the setup script is trying to connect to PostgreSQL on localhost, but the database runs inside Docker containers.

## ✅ Solution: Follow These Steps

### Option 1: Automated Setup (Recommended)

**For Windows:**
```bash
# Run the automated setup script
scripts\docker_setup.bat
```

**For Linux/Mac:**
```bash
# Make script executable and run
chmod +x scripts/docker_setup.sh
./scripts/docker_setup.sh
```

### Option 2: Manual Setup

#### Step 1: Start the Database
```bash
# Start only PostgreSQL first
docker-compose up -d postgres

# Wait 10 seconds for it to initialize
```

#### Step 2: Setup Database Schema
```bash
# Run the setup inside the API container
docker-compose run --rm api-service python /app/scripts/setup_login_system.py
```

#### Step 3: Install Frontend Dependencies
```bash
cd frontend
npm install
cd ..
```

#### Step 4: Start All Services
```bash
# Build and start everything
docker-compose up --build
```

## 🎯 Access the Application

Once setup is complete:
- **Frontend**: http://localhost:3000
- **API Docs**: http://localhost:8000/docs

## 👤 Test Credentials

| Username | Password | Client | Role |
|----------|----------|---------|------|
| `admin_acme` | `password123` | ACME Corporation | Admin |
| `user_acme` | `password123` | ACME Corporation | User |
| `admin_tech` | `password123` | Tech Solutions Ltd | Admin |

## 🔧 Troubleshooting

### Database Connection Issues
If you still get connection errors:

1. **Check if PostgreSQL is running:**
   ```bash
   docker-compose ps postgres
   ```

2. **View PostgreSQL logs:**
   ```bash
   docker-compose logs postgres
   ```

3. **Reset everything:**
   ```bash
   docker-compose down -v
   docker-compose up --build
   ```

### Frontend Issues
If the React app doesn't load:

1. **Check if dependencies are installed:**
   ```bash
   cd frontend
   npm install
   ```

2. **Rebuild frontend container:**
   ```bash
   docker-compose up --build frontend
   ```

### API Issues
If the API doesn't respond:

1. **Check API service logs:**
   ```bash
   docker-compose logs api-service
   ```

2. **Verify all dependencies are installed:**
   ```bash
   docker-compose build api-service
   ```

## 🚀 What the Login System Provides

✅ **Client Isolation**: Each client only sees their own invoice records  
✅ **Secure Authentication**: JWT tokens with bcrypt password hashing  
✅ **Modern UI**: Responsive React interface  
✅ **Protected Routes**: Authentication required for dashboard access  
✅ **Role-based Access**: Admin and user roles within each client  

## 📝 Next Steps After Setup

1. **Login** with any of the test credentials
2. **Explore** the dashboard showing client-specific invoices
3. **Test** logout and login with different users
4. **Verify** data isolation between clients

## 🆘 Still Having Issues?

If you continue to have problems:

1. **Check Docker is running**: `docker --version`
2. **Check Docker Compose**: `docker-compose --version`
3. **View all logs**: `docker-compose logs`
4. **Check ports**: Make sure ports 3000, 8000, and 5432 are available

The login system is fully implemented and ready to use once the Docker containers are properly started!
