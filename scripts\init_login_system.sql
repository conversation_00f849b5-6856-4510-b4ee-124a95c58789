-- Initialize the login system with sample data
-- This script creates the necessary tables and sample users/clients

-- Create clients table if it doesn't exist
CREATE TABLE IF NOT EXISTS clients (
    id SERIAL PRIMARY KEY,
    client_id VARCHAR(100) UNIQUE NOT NULL,
    company_name VA<PERSON>HAR(255) NOT NULL,
    email_domain VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT NOW()
);

-- Create users table if it doesn't exist
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VA<PERSON>HAR(255),
    client_id INTEGER REFERENCES clients(id),
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP
);

-- Add client_id column to email_records if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'email_records' AND column_name = 'client_id') THEN
        ALTER TABLE email_records ADD COLUMN client_id INTEGER REFERENCES clients(id);
    END IF;
END $$;

-- Insert sample clients (only if they don't exist)
INSERT INTO clients (client_id, company_name, email_domain, is_active)
SELECT 'ACME_CORP', 'ACME Corporation', 'acme.com', TRUE
WHERE NOT EXISTS (SELECT 1 FROM clients WHERE client_id = 'ACME_CORP');

INSERT INTO clients (client_id, company_name, email_domain, is_active)
SELECT 'TECH_SOLUTIONS', 'Tech Solutions Ltd', 'techsolutions.com', TRUE
WHERE NOT EXISTS (SELECT 1 FROM clients WHERE client_id = 'TECH_SOLUTIONS');

-- Insert sample users (only if they don't exist)
-- Note: Password is 'password123' hashed with bcrypt
INSERT INTO users (username, email, hashed_password, full_name, client_id, is_active, is_admin)
SELECT 'admin_acme', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', 'ACME Administrator', 
       (SELECT id FROM clients WHERE client_id = 'ACME_CORP'), TRUE, TRUE
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin_acme');

INSERT INTO users (username, email, hashed_password, full_name, client_id, is_active, is_admin)
SELECT 'user_acme', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', 'ACME User', 
       (SELECT id FROM clients WHERE client_id = 'ACME_CORP'), TRUE, FALSE
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'user_acme');

INSERT INTO users (username, email, hashed_password, full_name, client_id, is_active, is_admin)
SELECT 'admin_tech', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', 'Tech Solutions Administrator', 
       (SELECT id FROM clients WHERE client_id = 'TECH_SOLUTIONS'), TRUE, TRUE
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin_tech');

-- Update existing email_records to have a default client_id (assign to first client)
UPDATE email_records 
SET client_id = (SELECT id FROM clients ORDER BY id LIMIT 1)
WHERE client_id IS NULL;

-- Display success message
SELECT 'Login system initialized successfully!' as message;
SELECT 'Sample users created with password: password123' as info;
