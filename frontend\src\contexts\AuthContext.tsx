import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { AuthContextType, User, Client, LoginRequest } from '../types/auth'
import { authApi } from '../services/api'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [client, setClient] = useState<Client | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user && !!token

  // Load token from localStorage on mount
  useEffect(() => {
    const savedToken = localStorage.getItem('auth_token')
    if (savedToken) {
      setToken(savedToken)
      // Verify token and get user info
      verifyToken(savedToken)
    } else {
      setIsLoading(false)
    }
  }, [])

  const verifyToken = async (authToken: string) => {
    try {
      const response = await authApi.getCurrentUser(authToken)
      setUser(response.user)
      setClient(response.client)
      setToken(authToken)
    } catch (error) {
      console.error('Token verification failed:', error)
      // Clear invalid token
      localStorage.removeItem('auth_token')
      setToken(null)
      setUser(null)
      setClient(null)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (credentials: LoginRequest) => {
    try {
      setIsLoading(true)
      const response = await authApi.login(credentials)
      
      // Store token
      localStorage.setItem('auth_token', response.access_token)
      
      // Update state
      setToken(response.access_token)
      setUser(response.user)
      setClient(response.client)
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    // Clear localStorage
    localStorage.removeItem('auth_token')
    
    // Clear state
    setToken(null)
    setUser(null)
    setClient(null)
  }

  const value: AuthContextType = {
    user,
    client,
    token,
    isLoading,
    login,
    logout,
    isAuthenticated,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
