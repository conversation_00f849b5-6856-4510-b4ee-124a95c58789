"""
Pydantic schemas for file processing service
"""

from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class FileProcessRequest(BaseModel):
    """
    Request schema for file processing
    """
    file_path: str
    email_id: Optional[str] = None
    preserve_structure: bool = True

class ExtractedFile(BaseModel):
    """
    Schema for extracted file information
    """
    filename: str
    file_type: str  # xml, pdf, other
    file_path: str
    file_size: int
    extraction_date: datetime

class FileProcessResponse(BaseModel):
    """
    Response schema for file processing
    """
    success: bool
    message: str
    extracted_files: List[ExtractedFile]
    xml_files: List[ExtractedFile]
    pdf_files: List[ExtractedFile]
    other_files: List[ExtractedFile]
    errors: Optional[List[str]] = None

class BatchProcessRequest(BaseModel):
    """
    Request schema for batch file processing
    """
    file_paths: List[str]
    email_ids: Optional[List[str]] = None

class BatchProcessResponse(BaseModel):
    """
    Response schema for batch file processing
    """
    success: bool
    message: str
    processed_count: int
    successful_files: List[str]
    failed_files: List[str]
    total_extracted_files: int
