"""
File Processing Microservice
Handles ZIP file extraction and file organization
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, UploadFile, File
from fastapi.responses import JSONResponse
import uvicorn
import os
import uuid
from typing import List, Optional
from datetime import datetime
from sqlalchemy.orm import Session

from shared.schemas.file_processing import FileProcessRequest, FileProcessResponse, ExtractedFile
from shared.database.connection import get_db
from shared.utils.logger import get_logger
from shared.utils.file_utils import (
    extract_zip_file,
    categorize_files,
    get_file_size,
    ensure_directory,
    is_zip_file
)
from shared.database.models import ExtractedFileRecord, ZipFileRecord

# Initialize FastAPI app
app = FastAPI(
    title="File Processing Service",
    description="Microservice for processing ZIP files and organizing content",
    version="1.0.0"
)

logger = get_logger(__name__)

# Configuration
EXTRACTED_FILES_PATH = os.getenv("EXTRACTED_FILES_PATH", "./extracted_files")
ensure_directory(EXTRACTED_FILES_PATH)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "file-processing-service"}

# File processing endpoints
@app.post("/process-zip", response_model=FileProcessResponse)
async def process_zip_file(
    request: FileProcessRequest,
    db: Session = Depends(get_db)
):
    """
    Process ZIP file and organize contents
    """
    try:
        logger.info(f"Processing ZIP file: {request.file_path}")

        # Validate ZIP file exists and is valid
        if not os.path.exists(request.file_path):
            raise HTTPException(status_code=404, detail=f"ZIP file not found: {request.file_path}")

        if not is_zip_file(request.file_path):
            raise HTTPException(status_code=400, detail=f"Invalid ZIP file: {request.file_path}")

        # Create extraction directory
        extraction_id = str(uuid.uuid4())
        extraction_dir = os.path.join(EXTRACTED_FILES_PATH, extraction_id)
        ensure_directory(extraction_dir)

        # Extract ZIP file contents
        logger.info(f"Extracting ZIP file to: {extraction_dir}")
        try:
            extracted_file_paths = extract_zip_file(request.file_path, extraction_dir)
            if not extracted_file_paths:
                logger.warning(f"No files extracted from ZIP: {request.file_path}")
                return FileProcessResponse(
                    success=True,
                    message="ZIP file processed but no files were extracted.",
                    extracted_files=[],
                    xml_files=[],
                    pdf_files=[],
                    other_files=[]
                )
        except Exception as e:
            logger.error(f"Failed to extract ZIP file {request.file_path}: {str(e)}")
            raise HTTPException(status_code=400, detail=f"ZIP extraction failed: {str(e)}")

        # Find or create ZIP file record
        zip_file_record = db.query(ZipFileRecord).filter(
            ZipFileRecord.file_path == request.file_path
        ).first()

        if not zip_file_record:
            logger.warning(f"ZIP file record not found for {request.file_path}. Creating temporary record.")
            zip_file_record = ZipFileRecord(
                filename=os.path.basename(request.file_path),
                file_path=request.file_path,
                file_size=get_file_size(request.file_path),
                email_id=request.email_id or "unknown",
                download_date=datetime.now()
            )
            db.add(zip_file_record)
            db.flush()  # Get the ID

        # Categorize extracted files
        xml_file_paths, pdf_file_paths, other_file_paths = categorize_files(extracted_file_paths)

        # Create file records
        all_extracted_files = []
        xml_files = []
        pdf_files = []
        other_files = []

        # Process XML files
        for file_path in xml_file_paths:
            extracted_file = _create_extracted_file_record(file_path, "xml", zip_file_record.id, db)
            all_extracted_files.append(extracted_file)
            xml_files.append(extracted_file)

        # Process PDF files
        for file_path in pdf_file_paths:
            extracted_file = _create_extracted_file_record(file_path, "pdf", zip_file_record.id, db)
            all_extracted_files.append(extracted_file)
            pdf_files.append(extracted_file)

        # Process other files
        for file_path in other_file_paths:
            extracted_file = _create_extracted_file_record(file_path, "other", zip_file_record.id, db)
            all_extracted_files.append(extracted_file)
            other_files.append(extracted_file)

        db.commit()

        logger.info(f"Successfully processed ZIP file. Extracted {len(all_extracted_files)} files: "
                   f"{len(xml_files)} XML, {len(pdf_files)} PDF, {len(other_files)} other")

        return FileProcessResponse(
            success=True,
            message=f"ZIP file processed successfully. Extracted {len(all_extracted_files)} files.",
            extracted_files=all_extracted_files,
            xml_files=xml_files,
            pdf_files=pdf_files,
            other_files=other_files
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error processing ZIP file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"ZIP processing failed: {str(e)}")

def _create_extracted_file_record(file_path: str, file_type: str, zip_file_record_id: int, db: Session) -> ExtractedFile:
    """
    Create an ExtractedFile record from a file path
    """
    try:
        filename = os.path.basename(file_path)

        # Validate file exists
        if not os.path.exists(file_path):
            logger.error(f"Extracted file not found: {file_path}")
            raise FileNotFoundError(f"Extracted file not found: {file_path}")

        file_size = get_file_size(file_path)
        extraction_date = datetime.now()

        # Create database record
        db_record = ExtractedFileRecord(
            zip_file_record_id=zip_file_record_id,
            filename=filename,
            file_type=file_type,
            file_path=file_path,
            file_size=file_size,
            extraction_date=extraction_date
        )
        db.add(db_record)

        logger.debug(f"Created extracted file record: {filename} ({file_type}, {file_size} bytes)")

        # Return schema object
        return ExtractedFile(
            filename=filename,
            file_type=file_type,
            file_path=file_path,
            file_size=file_size,
            extraction_date=extraction_date
        )
    except Exception as e:
        logger.error(f"Failed to create extracted file record for {file_path}: {str(e)}")
        raise

@app.post("/upload-zip")
async def upload_zip_file(file: UploadFile = File(...)):
    """
    Upload and process ZIP file
    """
    try:
        if not file.filename.endswith('.zip'):
            raise HTTPException(status_code=400, detail="Only ZIP files are allowed")

        # Save uploaded file to temp directory
        temp_dir = os.path.join(EXTRACTED_FILES_PATH, "uploads")
        ensure_directory(temp_dir)

        file_path = os.path.join(temp_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        logger.info(f"Uploaded file saved to: {file_path}")

        return {
            "filename": file.filename,
            "file_path": file_path,
            "message": "File uploaded successfully. Use /process-zip endpoint to process it."
        }

    except Exception as e:
        logger.error(f"Error uploading ZIP file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")

@app.get("/extracted-files/{zip_file_id}")
async def get_extracted_files(zip_file_id: int, db: Session = Depends(get_db)):
    """Get all extracted files for a specific ZIP file"""
    try:
        extracted_files = db.query(ExtractedFileRecord).filter(
            ExtractedFileRecord.zip_file_record_id == zip_file_id
        ).all()

        return {
            "zip_file_id": zip_file_id,
            "extracted_files": [
                {
                    "id": file.id,
                    "filename": file.filename,
                    "file_type": file.file_type,
                    "file_path": file.file_path,
                    "file_size": file.file_size,
                    "extraction_date": file.extraction_date
                }
                for file in extracted_files
            ]
        }
    except Exception as e:
        logger.error(f"Error retrieving extracted files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve extracted files: {str(e)}")

@app.get("/status")
async def get_processing_status():
    """Get current processing status"""
    return {
        "service": "file-processing-service",
        "status": "running",
        "extracted_files_path": EXTRACTED_FILES_PATH
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8002)),
        reload=os.getenv("DEBUG", "false").lower() == "true"
    )
