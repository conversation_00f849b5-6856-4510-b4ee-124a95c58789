import React, { useState, useCallback } from 'react'
import { fileProcessingApi } from '../services/api'
import LoadingSpinner from './LoadingSpinner'

interface FileUploadProps {
  onUploadSuccess?: (result: any) => void
  onUploadError?: (error: string) => void
}

const FileUpload: React.FC<FileUploadProps> = ({ onUploadSuccess, onUploadError }) => {
  const [isDragOver, setIsDragOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<string>('')

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    handleFiles(files)
  }, [])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    handleFiles(files)
  }, [])

  const handleFiles = async (files: File[]) => {
    const zipFiles = files.filter(file => file.name.toLowerCase().endsWith('.zip'))
    
    if (zipFiles.length === 0) {
      onUploadError?.('Please select ZIP files containing invoices.')
      return
    }

    for (const file of zipFiles) {
      await processFile(file)
    }
  }

  const processFile = async (file: File) => {
    try {
      setIsUploading(true)
      setUploadProgress(`Uploading ${file.name}...`)

      // Upload the file
      const uploadResult = await fileProcessingApi.uploadFile(file)
      setUploadProgress(`Processing ${file.name}...`)

      // Process the uploaded file
      const processResult = await fileProcessingApi.processZipFile(uploadResult.file_path)
      setUploadProgress(`Extracting CUFE codes from ${file.name}...`)

      // The processing pipeline should handle CUFE extraction automatically
      setUploadProgress(`Completed processing ${file.name}`)
      
      onUploadSuccess?.({
        filename: file.name,
        uploadResult,
        processResult
      })

    } catch (error: any) {
      console.error('File processing error:', error)
      onUploadError?.(error.response?.data?.detail || `Failed to process ${file.name}`)
    } finally {
      setIsUploading(false)
      setUploadProgress('')
    }
  }

  return (
    <div className="w-full">
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${isDragOver 
            ? 'border-primary-400 bg-primary-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${isUploading ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => document.getElementById('file-input')?.click()}
      >
        <input
          id="file-input"
          type="file"
          multiple
          accept=".zip"
          onChange={handleFileSelect}
          className="hidden"
        />

        {isUploading ? (
          <div className="flex flex-col items-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-sm text-gray-600">{uploadProgress}</p>
          </div>
        ) : (
          <>
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <div className="mt-4">
              <p className="text-lg font-medium text-gray-900">
                Drop ZIP files here or click to browse
              </p>
              <p className="mt-2 text-sm text-gray-500">
                Upload ZIP files containing Colombian electronic invoices (XML format)
              </p>
              <p className="mt-1 text-xs text-gray-400">
                Supported format: .zip files with UBL 2.1 XML invoices
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default FileUpload
