#!/usr/bin/env python3
"""
Quick fix script to add the missing client_id column to email_records table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from shared.database.connection import engine

def fix_database():
    """Add missing client_id column to email_records table"""
    print("🔧 Fixing database schema...")
    
    try:
        with engine.connect() as connection:
            # Check if client_id column exists
            result = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'email_records' AND column_name = 'client_id'
            """))
            
            if result.fetchone():
                print("✅ client_id column already exists in email_records")
                return True
            
            # Add the client_id column
            print("🔄 Adding client_id column to email_records table...")
            connection.execute(text("""
                ALTER TABLE email_records 
                ADD COLUMN client_id INTEGER REFERENCES clients(id)
            """))
            connection.commit()
            print("✅ client_id column added successfully")
            
            # Update existing records to have a default client_id
            print("🔄 Updating existing email records...")
            result = connection.execute(text("""
                UPDATE email_records 
                SET client_id = (SELECT id FROM clients ORDER BY id LIMIT 1)
                WHERE client_id IS NULL
            """))
            connection.commit()
            
            if result.rowcount > 0:
                print(f"✅ Updated {result.rowcount} email records with client_id")
            else:
                print("✅ No email records needed updating")
            
            return True
            
    except Exception as e:
        print(f"❌ Database fix failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Database Fix Script")
    print("=" * 30)
    
    if fix_database():
        print("\n🎉 Database fix completed successfully!")
        print("The login system should now work properly.")
    else:
        print("\n❌ Database fix failed.")
        sys.exit(1)
