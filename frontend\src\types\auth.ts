export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  client_id: number
  is_active: boolean
  is_admin: boolean
  created_date: string
  last_login?: string
}

export interface Client {
  id: number
  client_id: string
  company_name: string
  email_domain?: string
  is_active: boolean
  created_date: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  user: User
  client: Client
}

export interface AuthContextType {
  user: User | null
  client: Client | null
  token: string | null
  isLoading: boolean
  login: (credentials: LoginRequest) => Promise<void>
  logout: () => void
  isAuthenticated: boolean
}
