"""
<PERSON><PERSON><PERSON> to create sample users and clients for testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from shared.database.connection import get_db, create_tables
from shared.database.models import User, Client
from shared.utils.password import hash_password

def create_sample_data():
    """Create sample clients and users for testing"""
    
    # Create tables first
    create_tables()
    
    # Get database session
    db = next(get_db())
    
    try:
        # Create sample clients
        client1 = Client(
            client_id="ACME_CORP",
            company_name="ACME Corporation",
            email_domain="acme.com",
            is_active=True
        )
        
        client2 = Client(
            client_id="TECH_SOLUTIONS",
            company_name="Tech Solutions Ltd",
            email_domain="techsolutions.com",
            is_active=True
        )
        
        db.add(client1)
        db.add(client2)
        db.commit()
        
        # Refresh to get IDs
        db.refresh(client1)
        db.refresh(client2)
        
        # Create sample users
        user1 = User(
            username="admin_acme",
            email="<EMAIL>",
            hashed_password=hash_password("password123"),
            full_name="ACME Administrator",
            client_id=client1.id,
            is_active=True,
            is_admin=True
        )
        
        user2 = User(
            username="user_acme",
            email="<EMAIL>",
            hashed_password=hash_password("password123"),
            full_name="ACME User",
            client_id=client1.id,
            is_active=True,
            is_admin=False
        )
        
        user3 = User(
            username="admin_tech",
            email="<EMAIL>",
            hashed_password=hash_password("password123"),
            full_name="Tech Solutions Administrator",
            client_id=client2.id,
            is_active=True,
            is_admin=True
        )
        
        db.add(user1)
        db.add(user2)
        db.add(user3)
        db.commit()
        
        print("Sample data created successfully!")
        print("\nSample Users:")
        print("1. Username: admin_acme, Password: password123 (ACME Corp Admin)")
        print("2. Username: user_acme, Password: password123 (ACME Corp User)")
        print("3. Username: admin_tech, Password: password123 (Tech Solutions Admin)")
        
    except Exception as e:
        print(f"Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_data()
