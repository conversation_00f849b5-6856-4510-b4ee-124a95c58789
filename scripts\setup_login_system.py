#!/usr/bin/env python3
"""
Setup script for the login system
This script initializes the database with the new authentication models
and creates sample data for testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from shared.database.connection import get_db, create_tables, engine
from shared.database.models import User, Client, EmailRecord
from shared.utils.password import hash_password

def setup_database():
    """Setup database with new authentication models"""
    print("🔧 Setting up database...")
    
    try:
        # Create all tables
        create_tables()
        print("✅ Database tables created successfully")
        
        # Get database session
        db = next(get_db())
        
        # Check if we need to add client_id column to existing email_records
        try:
            # Try to query the client_id column
            result = db.execute(text("SELECT client_id FROM email_records LIMIT 1"))
            print("✅ client_id column already exists in email_records")
        except Exception:
            # Column doesn't exist, add it
            print("🔄 Adding client_id column to email_records table...")
            db.execute(text("ALTER TABLE email_records ADD COLUMN client_id INTEGER REFERENCES clients(id)"))
            db.commit()
            print("✅ client_id column added to email_records")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False
    
    return True

def create_sample_clients_and_users():
    """Create sample clients and users for testing"""
    print("👥 Creating sample clients and users...")
    
    db = next(get_db())
    
    try:
        # Check if clients already exist
        existing_client = db.query(Client).first()
        if existing_client:
            print("✅ Sample clients already exist")
            db.close()
            return True
        
        # Create sample clients
        client1 = Client(
            client_id="ACME_CORP",
            company_name="ACME Corporation",
            email_domain="acme.com",
            is_active=True
        )
        
        client2 = Client(
            client_id="TECH_SOLUTIONS",
            company_name="Tech Solutions Ltd",
            email_domain="techsolutions.com",
            is_active=True
        )
        
        db.add(client1)
        db.add(client2)
        db.commit()
        
        # Refresh to get IDs
        db.refresh(client1)
        db.refresh(client2)
        
        # Create sample users
        users = [
            User(
                username="admin_acme",
                email="<EMAIL>",
                hashed_password=hash_password("password123"),
                full_name="ACME Administrator",
                client_id=client1.id,
                is_active=True,
                is_admin=True
            ),
            User(
                username="user_acme",
                email="<EMAIL>",
                hashed_password=hash_password("password123"),
                full_name="ACME User",
                client_id=client1.id,
                is_active=True,
                is_admin=False
            ),
            User(
                username="admin_tech",
                email="<EMAIL>",
                hashed_password=hash_password("password123"),
                full_name="Tech Solutions Administrator",
                client_id=client2.id,
                is_active=True,
                is_admin=True
            )
        ]
        
        for user in users:
            db.add(user)
        
        db.commit()
        
        print("✅ Sample clients and users created successfully!")
        print("\n📋 Sample Login Credentials:")
        print("1. Username: admin_acme, Password: password123 (ACME Corp Admin)")
        print("2. Username: user_acme, Password: password123 (ACME Corp User)")
        print("3. Username: admin_tech, Password: password123 (Tech Solutions Admin)")
        
    except Exception as e:
        print(f"❌ Failed to create sample data: {e}")
        db.rollback()
        return False
    finally:
        db.close()
    
    return True

def update_existing_email_records():
    """Update existing email records to have a default client_id"""
    print("📧 Updating existing email records...")
    
    db = next(get_db())
    
    try:
        # Get the first client
        first_client = db.query(Client).first()
        if not first_client:
            print("⚠️ No clients found, skipping email record updates")
            return True
        
        # Update email records without client_id
        result = db.execute(
            text("UPDATE email_records SET client_id = :client_id WHERE client_id IS NULL"),
            {"client_id": first_client.id}
        )
        
        db.commit()
        
        if result.rowcount > 0:
            print(f"✅ Updated {result.rowcount} email records with client_id")
        else:
            print("✅ No email records needed updating")
        
    except Exception as e:
        print(f"❌ Failed to update email records: {e}")
        db.rollback()
        return False
    finally:
        db.close()
    
    return True

def main():
    """Main setup function"""
    print("🚀 Setting up CUFE Login System")
    print("=" * 50)
    print("\n⚠️  IMPORTANT: This script should be run AFTER starting the Docker containers!")
    print("Please make sure you have run: docker-compose up -d postgres")
    print("Or run the full stack: docker-compose up --build")
    print("\nPress Enter to continue or Ctrl+C to cancel...")

    try:
        input()
    except KeyboardInterrupt:
        print("\n❌ Setup cancelled by user.")
        sys.exit(0)

    # Step 1: Setup database
    if not setup_database():
        print("❌ Database setup failed. Make sure PostgreSQL is running in Docker.")
        print("💡 Try: docker-compose up -d postgres")
        sys.exit(1)

    # Step 2: Create sample data
    if not create_sample_clients_and_users():
        print("❌ Sample data creation failed. Exiting.")
        sys.exit(1)

    # Step 3: Update existing records
    if not update_existing_email_records():
        print("❌ Email record updates failed. Exiting.")
        sys.exit(1)

    print("\n🎉 Login system setup completed successfully!")
    print("\n📝 Next Steps:")
    print("1. Install frontend dependencies: cd frontend && npm install")
    print("2. Build and start the services: docker-compose up --build")
    print("3. Access the application at: http://localhost:3000")
    print("4. Use the sample credentials above to test the login system")

if __name__ == "__main__":
    main()
