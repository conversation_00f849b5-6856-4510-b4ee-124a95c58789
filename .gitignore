# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv
venv/
ENV/
env/
.env/

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/

# Node.js / React Frontend
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Frontend build outputs
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/

# Logs
logs
*.log

# Docker
.dockerignore

# Database
*.db
*.sqlite
*.sqlite3

# File uploads and downloads
uploads/
downloads/
temp/
tmp/

# ZIP files and archives
*.zip
*.tar.gz
*.rar
*.7z

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Email attachments and processed files
email_attachments/
processed_files/
extracted_files/

# Backup files
*.bak
*.backup
*.old

# IDE files
*.swp
*.swo
*~

# Certificate files
*.pem
*.key
*.crt

# Local configuration overrides
config/local.py
local_settings.py
