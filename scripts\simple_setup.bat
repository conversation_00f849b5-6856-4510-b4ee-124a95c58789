@echo off
echo 🚀 Simple Setup for CUFE Login System
echo =====================================

echo 📦 Step 1: Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo 📦 Step 2: Starting PostgreSQL...
docker-compose up -d postgres
if %errorlevel% neq 0 (
    echo ❌ Failed to start PostgreSQL
    pause
    exit /b 1
)

echo ⏳ Step 3: Waiting for PostgreSQL to be ready...
timeout /t 15 /nobreak > nul

echo 🔧 Step 4: Setting up database schema and sample data...
docker-compose exec -T postgres psql -U cufe_user -d cufe_db < scripts/init_login_system.sql
if %errorlevel% neq 0 (
    echo ❌ Failed to setup database. Trying alternative method...
    echo 🔄 Creating tables through API service...
    docker-compose run --rm api-service python -c "
from shared.database.connection import create_tables
create_tables()
print('✅ Tables created successfully')
"
)

echo 🏗️ Step 5: Building and starting all services...
docker-compose up --build -d
if %errorlevel% neq 0 (
    echo ❌ Failed to start services
    pause
    exit /b 1
)

echo.
echo 🎉 Setup completed successfully!
echo.
echo 📋 Sample Login Credentials:
echo Username: admin_acme, Password: password123 (ACME Corp Admin)
echo Username: user_acme, Password: password123 (ACME Corp User)
echo Username: admin_tech, Password: password123 (Tech Solutions Admin)
echo.
echo 🌐 Access the application at: http://localhost:3000
echo 📚 API Documentation at: http://localhost:8000/docs
echo.
echo 📝 To view logs: docker-compose logs -f
echo 🛑 To stop: docker-compose down
echo.
pause
