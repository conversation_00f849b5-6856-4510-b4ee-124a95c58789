"""
Application settings and configuration
"""

import os
from typing import Optional
from pydantic import BaseSettings, EmailStr

class Settings(BaseSettings):
    """
    Application settings loaded from environment variables
    """
    
    # Database settings
    database_url: str = "postgresql://cufe_user:cufe_password@localhost:5432/cufe_db"
    
    # Email settings
    email_host: str = "imap.gmail.com"
    email_port: int = 993
    email_username: Optional[EmailStr] = None
    email_password: Optional[str] = None
    email_use_ssl: bool = True
    
    # JWT settings
    jwt_secret_key: str = "your_super_secret_jwt_key_here"
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    
    # File storage settings
    temp_files_path: str = "./temp_files"
    processed_files_path: str = "./processed_files"
    
    # Service URLs
    mailbox_service_url: str = "http://localhost:8001"
    file_processing_service_url: str = "http://localhost:8002"
    extraction_service_url: str = "http://localhost:8003"
    api_service_url: str = "http://localhost:8000"
    
    # Application settings
    debug: bool = False
    log_level: str = "INFO"
    log_to_file: bool = False
    log_dir: str = "logs"
    
    # Cloud storage settings (optional)
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_region: str = "us-east-1"
    s3_bucket_name: Optional[str] = None
    
    # Processing settings
    max_emails_per_batch: int = 100
    max_file_size_mb: int = 50
    supported_file_types: list = ["xml", "pdf"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()

def get_settings() -> Settings:
    """
    Get application settings
    
    Returns:
        Settings instance
    """
    return settings
