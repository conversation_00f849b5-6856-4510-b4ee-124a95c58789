"""
Pydantic schemas for extraction service
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class ExtractionRequest(BaseModel):
    """
    Request schema for CUFE extraction
    """
    xml_file_path: str
    email_id: Optional[str] = None
    extract_additional_data: bool = True

class CUFEData(BaseModel):
    """
    Schema for CUFE and additional extracted data
    """
    cufe_value: str
    issuer_name: Optional[str] = None
    document_number: Optional[str] = None
    issue_date: Optional[datetime] = None
    total_amount: Optional[str] = None
    additional_fields: Optional[Dict[str, Any]] = None

class ExtractionResponse(BaseModel):
    """
    Response schema for CUFE extraction
    """
    success: bool
    cufe_value: str
    xml_file_path: str
    message: str
    cufe_data: Optional[CUFEData] = None
    extraction_date: datetime = datetime.now()
    errors: Optional[List[str]] = None

class BatchExtractionRequest(BaseModel):
    """
    Request schema for batch CUFE extraction
    """
    xml_file_paths: List[str]
    email_ids: Optional[List[str]] = None

class BatchExtractionResult(BaseModel):
    """
    Schema for individual batch extraction result
    """
    xml_file_path: str
    success: bool
    cufe_value: Optional[str] = None
    cufe_data: Optional[CUFEData] = None
    error_message: Optional[str] = None

class BatchExtractionResponse(BaseModel):
    """
    Response schema for batch CUFE extraction
    """
    success: bool
    message: str
    processed_count: int
    successful_extractions: int
    failed_extractions: int
    results: List[BatchExtractionResult]
