"""
SQLAlchemy database models
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Text, Boolean, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .connection import Base

class Client(Base):
    """
    Model for storing client information
    """
    __tablename__ = "clients"

    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(String(100), unique=True, index=True, nullable=False)
    company_name = Column(String(255), nullable=False)
    email_domain = Column(String(255), nullable=True)  # For automatic client assignment
    created_date = Column(DateTime, default=func.now())
    is_active = Column(Boolean, default=True)

    # Relationships
    users = relationship("User", back_populates="client")
    email_records = relationship("EmailRecord", back_populates="client")

class User(Base):
    """
    Model for storing user authentication information
    """
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_date = Column(DateTime, default=func.now())
    last_login = Column(DateTime, nullable=True)

    # Relationships
    client = relationship("Client", back_populates="users")

class EmailRecord(Base):
    """
    Model for storing email processing records
    """
    __tablename__ = "email_records"

    id = Column(Integer, primary_key=True, index=True)
    email_id = Column(String(255), unique=True, index=True, nullable=False)
    sender = Column(String(255), nullable=False)
    subject = Column(Text)
    reception_date = Column(DateTime, nullable=False)
    processed_date = Column(DateTime, default=func.now())
    has_zip_attachment = Column(Boolean, default=False)
    processing_status = Column(String(50), default="pending")  # pending, processing, completed, failed
    error_message = Column(Text, nullable=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)

    # Relationships
    client = relationship("Client", back_populates="email_records")
    cufe_records = relationship("CUFERecord", back_populates="email_record")

class ZipFileRecord(Base):
    """
    Model for storing ZIP file processing records
    """
    __tablename__ = "zip_file_records"
    
    id = Column(Integer, primary_key=True, index=True)
    email_record_id = Column(Integer, ForeignKey("email_records.id"), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)
    download_date = Column(DateTime, default=func.now())
    extraction_status = Column(String(50), default="pending")  # pending, extracted, failed
    extracted_files_count = Column(Integer, default=0)
    
    # Relationship to email record
    email_record = relationship("EmailRecord")
    
    # Relationship to extracted files
    extracted_files = relationship("ExtractedFileRecord", back_populates="zip_file_record")

class ExtractedFileRecord(Base):
    """
    Model for storing extracted file records
    """
    __tablename__ = "extracted_file_records"
    
    id = Column(Integer, primary_key=True, index=True)
    zip_file_record_id = Column(Integer, ForeignKey("zip_file_records.id"), nullable=False)
    filename = Column(String(255), nullable=False)
    file_type = Column(String(10), nullable=False)  # xml, pdf, other
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)
    extraction_date = Column(DateTime, default=func.now())
    
    # Relationship to ZIP file record
    zip_file_record = relationship("ZipFileRecord", back_populates="extracted_files")
    
    # Relationship to CUFE records (for XML files)
    cufe_records_as_xml = relationship("CUFERecord", foreign_keys="CUFERecord.xml_file_record_id", back_populates="xml_file_record")
    cufe_records_as_pdf = relationship("CUFERecord", foreign_keys="CUFERecord.pdf_file_record_id", back_populates="pdf_file_record")

class CUFERecord(Base):
    """
    Model for storing CUFE extraction records
    """
    __tablename__ = "cufe_records"
    
    id = Column(Integer, primary_key=True, index=True)
    cufe_value = Column(String(255), unique=True, index=True, nullable=False)
    email_record_id = Column(Integer, ForeignKey("email_records.id"), nullable=False)
    xml_file_record_id = Column(Integer, ForeignKey("extracted_file_records.id"), nullable=False)
    pdf_file_record_id = Column(Integer, ForeignKey("extracted_file_records.id"), nullable=True)
    extraction_date = Column(DateTime, default=func.now())
    
    # Additional XML data that might be useful
    issuer_name = Column(String(255), nullable=True)
    document_number = Column(String(100), nullable=True)
    issue_date = Column(DateTime, nullable=True)
    total_amount = Column(String(50), nullable=True)
    
    # Relationships
    email_record = relationship("EmailRecord", back_populates="cufe_records")
    xml_file_record = relationship("ExtractedFileRecord", foreign_keys=[xml_file_record_id], back_populates="cufe_records_as_xml")
    pdf_file_record = relationship("ExtractedFileRecord", foreign_keys=[pdf_file_record_id], back_populates="cufe_records_as_pdf")

class ProcessingLog(Base):
    """
    Model for storing processing logs and audit trail
    """
    __tablename__ = "processing_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    service_name = Column(String(100), nullable=False)
    operation = Column(String(100), nullable=False)
    status = Column(String(50), nullable=False)  # success, error, warning
    message = Column(Text)
    details = Column(Text, nullable=True)  # JSON string for additional details
    timestamp = Column(DateTime, default=func.now())
    
    # Optional reference to related records
    email_record_id = Column(Integer, ForeignKey("email_records.id"), nullable=True)
    cufe_record_id = Column(Integer, ForeignKey("cufe_records.id"), nullable=True)
