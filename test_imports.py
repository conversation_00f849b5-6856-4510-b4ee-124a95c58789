#!/usr/bin/env python3
"""
Test script to check if all imports work correctly
"""

try:
    print("Testing basic imports...")
    from fastapi import FastAPI
    print("✓ FastAPI imported successfully")
    
    from pydantic import BaseModel, EmailStr
    print("✓ Pydantic imported successfully")
    
    from sqlalchemy import create_engine
    print("✓ SQLAlchemy imported successfully")
    
    print("\nTesting shared modules...")
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))
    
    from shared.database.connection import Base
    print("✓ Database connection imported successfully")
    
    from shared.schemas.api import ProcessEmailsRequest
    print("✓ API schemas imported successfully")
    
    print("\n✅ All imports successful!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
