#!/usr/bin/env python3
"""
Database initialization script
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.database.connection import create_tables, drop_tables, engine
from shared.database.models import Base
from shared.utils.logger import get_logger
from sqlalchemy import text

logger = get_logger(__name__)

def init_database(drop_existing: bool = False):
    """
    Initialize the database with all tables
    
    Args:
        drop_existing: Whether to drop existing tables first
    """
    try:
        logger.info("Starting database initialization...")
        
        if drop_existing:
            logger.info("Dropping existing tables...")
            drop_tables()
        
        logger.info("Creating database tables...")
        create_tables()
        
        # Verify tables were created
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """))
            tables = [row[0] for row in result]
            
        logger.info(f"Created tables: {', '.join(tables)}")
        logger.info("Database initialization completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        return False

def check_database_connection():
    """
    Check if database connection is working
    """
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            result.fetchone()
        
        logger.info("Database connection successful!")
        return True
        
    except Exception as e:
        logger.error(f"Database connection failed: {str(e)}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Initialize CUFE database")
    parser.add_argument(
        "--drop", 
        action="store_true", 
        help="Drop existing tables before creating new ones"
    )
    parser.add_argument(
        "--check", 
        action="store_true", 
        help="Only check database connection"
    )
    
    args = parser.parse_args()
    
    if args.check:
        success = check_database_connection()
    else:
        success = init_database(drop_existing=args.drop)
    
    sys.exit(0 if success else 1)
