"""
Pydantic schemas for authentication
"""

from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    """Base user schema"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    is_active: bool = True

class UserCreate(UserBase):
    """Schema for creating a new user"""
    password: str
    client_id: int

class UserUpdate(BaseModel):
    """Schema for updating user information"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class UserInDB(UserBase):
    """Schema for user stored in database"""
    id: int
    client_id: int
    is_admin: bool
    created_date: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class User(UserInDB):
    """Schema for user response"""
    pass

class ClientBase(BaseModel):
    """Base client schema"""
    client_id: str
    company_name: str
    email_domain: Optional[str] = None
    is_active: bool = True

class ClientCreate(ClientBase):
    """Schema for creating a new client"""
    pass

class ClientUpdate(BaseModel):
    """Schema for updating client information"""
    company_name: Optional[str] = None
    email_domain: Optional[str] = None
    is_active: Optional[bool] = None

class ClientInDB(ClientBase):
    """Schema for client stored in database"""
    id: int
    created_date: datetime
    
    class Config:
        from_attributes = True

class Client(ClientInDB):
    """Schema for client response"""
    pass

class LoginRequest(BaseModel):
    """Schema for login request"""
    username: str
    password: str

class LoginResponse(BaseModel):
    """Schema for login response"""
    access_token: str
    token_type: str
    user: User
    client: Client

class TokenData(BaseModel):
    """Schema for token data"""
    username: Optional[str] = None
    user_id: Optional[int] = None
    client_id: Optional[int] = None
