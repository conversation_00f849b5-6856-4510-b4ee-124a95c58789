"""
Pydantic schemas for main API service
"""

from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime

class ProcessEmailsRequest(BaseModel):
    """
    Request schema for processing emails through the complete pipeline
    """
    email_host: str
    email_port: int = 993
    email_username: EmailStr
    email_password: str
    use_ssl: bool = True
    folder: str = "INBOX"
    date_filter: Optional[str] = None
    max_emails: Optional[int] = 100

class CUFEResponse(BaseModel):
    """
    Response schema for CUFE information
    """
    cufe_value: str
    email_id: str
    reception_date: datetime
    xml_file_path: str
    pdf_file_path: Optional[str] = None
    processed_date: datetime
    
    # Additional CUFE data
    issuer_name: Optional[str] = None
    document_number: Optional[str] = None
    issue_date: Optional[datetime] = None
    total_amount: Optional[str] = None

class CUFEListResponse(BaseModel):
    """
    Response schema for listing CUFE records
    """
    records: List[CUFEResponse]
    total: int
    skip: int
    limit: int

class ProcessingStatus(BaseModel):
    """
    Schema for processing status information
    """
    status: str  # pending, processing, completed, failed
    message: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processed_emails: int = 0
    extracted_cufes: int = 0
    errors: Optional[List[str]] = None

class ServiceHealth(BaseModel):
    """
    Schema for service health information
    """
    service_name: str
    status: str  # healthy, unhealthy, degraded
    last_check: datetime
    response_time_ms: Optional[float] = None
    error_message: Optional[str] = None

class SystemStatus(BaseModel):
    """
    Schema for overall system status
    """
    overall_status: str
    services: List[ServiceHealth]
    last_updated: datetime
