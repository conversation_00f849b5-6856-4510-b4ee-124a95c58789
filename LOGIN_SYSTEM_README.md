# CUFE Login System

This document describes the newly implemented login system that provides client-specific authentication and data filtering for the CUFE invoice management system.

## 🎯 Overview

The login system implements:
- **Client-based Authentication**: Each client has their own isolated data
- **JWT Token Security**: Secure token-based authentication
- **React Frontend**: Modern, responsive user interface
- **Role-based Access**: Admin and user roles within each client
- **Data Isolation**: Each client only sees their own invoice records

## 🏗️ Architecture

### Backend (FastAPI)
- **Authentication Models**: `User`, `Client` tables with proper relationships
- **JWT Tokens**: Secure token generation and validation
- **Password Security**: bcrypt hashing for password storage
- **Client Filtering**: All CUFE queries filtered by authenticated user's client
- **CORS Support**: Configured for React frontend integration

### Frontend (React + TypeScript)
- **Modern Stack**: React 18, TypeScript, Tailwind CSS
- **Authentication Context**: Global auth state management
- **Protected Routes**: Route guards for authenticated access
- **Responsive Design**: Mobile-friendly interface
- **Token Management**: Automatic token refresh and storage

## 🚀 Quick Start

### 1. Setup the Login System
```bash
# Run the setup script to initialize database and create sample data
python scripts/setup_login_system.py
```

### 2. Install Frontend Dependencies
```bash
cd frontend
npm install
```

### 3. Start the System
```bash
# Build and start all services
docker-compose up --build
```

### 4. Access the Application
- **Frontend**: http://localhost:3000
- **API Documentation**: http://localhost:8000/docs

## 👤 Sample Users

The setup script creates these test accounts:

| Username | Password | Client | Role |
|----------|----------|---------|------|
| `admin_acme` | `password123` | ACME Corporation | Admin |
| `user_acme` | `password123` | ACME Corporation | User |
| `admin_tech` | `password123` | Tech Solutions Ltd | Admin |

## 🔐 Security Features

### Password Security
- **bcrypt Hashing**: Industry-standard password hashing
- **Salt Generation**: Automatic salt generation for each password
- **No Plain Text**: Passwords never stored in plain text

### JWT Tokens
- **Secure Generation**: HS256 algorithm with secret key
- **Expiration**: 30-minute token expiration (configurable)
- **User Context**: Tokens contain user ID and client ID
- **Automatic Refresh**: Frontend handles token refresh

### Data Isolation
- **Client Filtering**: All database queries filtered by client ID
- **API Security**: Protected endpoints require authentication
- **CORS Configuration**: Proper CORS setup for frontend integration

## 📊 Database Schema

### New Tables
```sql
-- Clients table
CREATE TABLE clients (
    id SERIAL PRIMARY KEY,
    client_id VARCHAR(100) UNIQUE NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    email_domain VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT NOW()
);

-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    client_id INTEGER REFERENCES clients(id),
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP
);
```

### Modified Tables
```sql
-- Added client_id to email_records
ALTER TABLE email_records 
ADD COLUMN client_id INTEGER REFERENCES clients(id);
```

## 🔧 API Endpoints

### Authentication
- `POST /auth/login` - User login
- `GET /auth/me` - Get current user info

### CUFE Records (Protected)
- `GET /cufe/` - List client's CUFE records
- `GET /cufe/{cufe_id}` - Get specific CUFE record

All protected endpoints require `Authorization: Bearer <token>` header.

## 🎨 Frontend Components

### Pages
- **LoginPage**: Authentication form with validation
- **DashboardPage**: Main interface showing client's invoices

### Components
- **AuthContext**: Global authentication state management
- **ProtectedRoute**: Route guard for authenticated access
- **LoadingSpinner**: Loading indicators

### Features
- **Form Validation**: Real-time form validation with error messages
- **Responsive Design**: Works on desktop and mobile devices
- **Error Handling**: Comprehensive error handling and user feedback
- **Token Management**: Automatic token storage and refresh

## 🛠️ Development

### Environment Variables
Create `.env` file in frontend directory:
```env
VITE_API_URL=http://localhost:8000
VITE_NODE_ENV=development
```

### Backend Configuration
Configure in `config/settings.py`:
```python
jwt_secret_key = "your_super_secret_jwt_key_here"
jwt_algorithm = "HS256"
jwt_access_token_expire_minutes = 30
```

### Running in Development
```bash
# Backend
cd services/api_service
uvicorn main:app --reload --port 8000

# Frontend
cd frontend
npm run dev
```

## 🔍 Testing

### Manual Testing
1. Access http://localhost:3000
2. Login with sample credentials
3. Verify dashboard shows client-specific data
4. Test logout functionality
5. Verify protected routes redirect to login

### API Testing
Use the interactive API docs at http://localhost:8000/docs to test endpoints.

## 🚨 Production Considerations

### Security
- Change default JWT secret key
- Use environment variables for sensitive configuration
- Implement rate limiting
- Add request logging
- Use HTTPS in production

### Performance
- Implement database connection pooling
- Add caching for frequently accessed data
- Optimize database queries
- Use CDN for static assets

### Monitoring
- Add application monitoring
- Implement health checks
- Set up error tracking
- Monitor authentication failures

## 📝 Troubleshooting

### Common Issues

**Login fails with "Invalid credentials"**
- Verify user exists in database
- Check password is correct
- Ensure user is active (`is_active = true`)

**Frontend shows "Network Error"**
- Verify API service is running on port 8000
- Check CORS configuration
- Verify proxy configuration in nginx.conf

**Database connection errors**
- Ensure PostgreSQL is running
- Check database credentials in docker-compose.yml
- Verify database tables exist

### Logs
Check logs for debugging:
```bash
# View API service logs
docker-compose logs api-service

# View frontend logs
docker-compose logs frontend

# View database logs
docker-compose logs postgres
```

## 🤝 Contributing

When making changes to the authentication system:
1. Update database migrations if schema changes
2. Update API documentation
3. Test with multiple clients
4. Verify data isolation works correctly
5. Update this documentation
