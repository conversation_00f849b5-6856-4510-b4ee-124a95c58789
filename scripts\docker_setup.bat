@echo off
echo 🚀 Setting up CUFE Login System with Docker
echo ================================================

REM Step 1: Start PostgreSQL container
echo 📦 Starting PostgreSQL container...
docker-compose up -d postgres

REM Wait for PostgreSQL to be ready
echo ⏳ Waiting for PostgreSQL to be ready...
timeout /t 10 /nobreak > nul

REM Step 2: Run database setup inside the API container
echo 🔧 Setting up database and creating sample data...
docker-compose run --rm api-service python scripts/setup_login_system.py

REM Step 3: Install frontend dependencies
echo 📦 Installing frontend dependencies...
cd frontend
call npm install
cd ..

REM Step 4: Build and start all services
echo 🏗️ Building and starting all services...
docker-compose up --build -d

echo.
echo 🎉 Setup completed successfully!
echo.
echo 📋 Sample Login Credentials:
echo 1. Username: admin_acme, Password: password123 (ACME Corp Admin)
echo 2. Username: user_acme, Password: password123 (ACME Corp User)
echo 3. Username: admin_tech, Password: password123 (Tech Solutions Admin)
echo.
echo 🌐 Access the application at: http://localhost:3000
echo 📚 API Documentation at: http://localhost:8000/docs
echo.
echo 📝 To view logs: docker-compose logs -f
echo 🛑 To stop: docker-compose down

pause
