<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Processing Demo - CUFE Invoice Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                    },
                },
            },
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Invoice Dashboard</h1>
                        <p class="text-sm text-gray-600 mt-1">Welcome back, Test User</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-900">Test Company</p>
                            <p class="text-xs text-gray-500">Client ID: TEST_CLIENT</p>
                        </div>
                        <button onclick="toggleEmailForm()" class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                            📧 Process Emails
                        </button>
                        <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                            Sign Out
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="px-4 py-6 sm:px-0">
                
                <!-- Email Processing Form -->
                <div id="emailForm" class="bg-white rounded-lg shadow-md p-6 mb-8 hidden">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Process Email Invoices</h2>
                    <p class="text-gray-600 mb-6">
                        Enter your email credentials to automatically download and process invoice attachments. 
                        The system will search for ZIP files containing invoices and extract CUFE codes.
                    </p>
                    
                    <div id="processingIndicator" class="hidden flex flex-col items-center py-8">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                        <p class="mt-4 text-sm text-gray-600" id="processingMessage">Processing...</p>
                    </div>
                    
                    <div id="emailFormFields" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email Host</label>
                                <input type="text" id="email_host" value="imap.gmail.com" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                                       placeholder="imap.gmail.com">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Port</label>
                                <input type="number" id="email_port" value="993"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input type="email" id="email_username" placeholder="<EMAIL>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password / App Password</label>
                            <input type="password" id="email_password" placeholder="Your email password or app password"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500" required>
                            <p class="mt-1 text-xs text-gray-500">For Gmail, use an App Password instead of your regular password</p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Folder</label>
                                <input type="text" id="folder" value="INBOX"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                                       placeholder="INBOX">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Max Emails</label>
                                <input type="number" id="max_emails" value="10" min="1" max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="use_ssl" checked
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                            <label class="ml-2 block text-sm text-gray-700">Use SSL/TLS (recommended)</label>
                        </div>
                        
                        <button onclick="processEmails()" class="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                            🚀 Start Processing Emails
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3h4v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Invoices</p>
                                <p class="text-2xl font-semibold text-gray-900" id="totalInvoices">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Processed</p>
                                <p class="text-2xl font-semibold text-gray-900" id="processedCount">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                                        <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Client</p>
                                <p class="text-lg font-semibold text-gray-900">TEST_CLIENT</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Section -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-gray-900">Recent Invoices</h2>
                        <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                            Refresh
                        </button>
                    </div>

                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No invoices found</h3>
                        <p class="mt-1 text-sm text-gray-500">
                            No invoice records have been processed for your account yet.
                        </p>
                        <p class="mt-2 text-sm text-primary-600">
                            Click "Process Emails" above to start processing your email invoices!
                        </p>
                    </div>
                </div>

            </div>
        </main>
    </div>

    <script>
        function toggleEmailForm() {
            const form = document.getElementById('emailForm');
            form.classList.toggle('hidden');
        }

        async function processEmails() {
            const emailData = {
                email_host: document.getElementById('email_host').value,
                email_port: parseInt(document.getElementById('email_port').value),
                email_username: document.getElementById('email_username').value,
                email_password: document.getElementById('email_password').value,
                use_ssl: document.getElementById('use_ssl').checked,
                folder: document.getElementById('folder').value,
                max_emails: parseInt(document.getElementById('max_emails').value)
            };

            if (!emailData.email_username || !emailData.email_password) {
                alert('Please provide email credentials');
                return;
            }

            // Show processing indicator
            document.getElementById('emailFormFields').classList.add('hidden');
            document.getElementById('processingIndicator').classList.remove('hidden');
            
            const messages = [
                'Connecting to email server...',
                'Searching for emails with attachments...',
                'Downloading ZIP files...',
                'Extracting XML files...',
                'Processing CUFE codes...',
                'Storing results in database...',
                'Processing completed successfully!'
            ];

            let messageIndex = 0;
            const messageElement = document.getElementById('processingMessage');
            
            const interval = setInterval(() => {
                if (messageIndex < messages.length) {
                    messageElement.textContent = messages[messageIndex];
                    messageIndex++;
                } else {
                    clearInterval(interval);
                    
                    // Simulate successful processing
                    setTimeout(() => {
                        document.getElementById('processingIndicator').classList.add('hidden');
                        document.getElementById('emailFormFields').classList.remove('hidden');
                        document.getElementById('emailForm').classList.add('hidden');
                        
                        // Update stats (simulated)
                        document.getElementById('totalInvoices').textContent = '5';
                        document.getElementById('processedCount').textContent = '5';
                        
                        alert('Email processing completed! 5 invoices were processed and CUFE codes extracted.');
                    }, 1000);
                }
            }, 1500);

            // In real implementation, this would call:
            // const response = await fetch('/api/process-emails', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(emailData)
            // });
        }
    </script>
</body>
</html>
