# CUFE Extraction Automation

A Python and FastAPI-based microservices solution to automate the process of connecting to a mailbox, downloading and processing .ZIP files, and extracting CUFE values from XML files.

## 🏗️ Architecture

This project follows a microservices architecture with the following components:

- **Mailbox Service** (`services/mailbox_service/`) - Connects to mailboxes via IMAP/POP3 and downloads ZIP attachments
- **File Processing Service** (`services/file_processing_service/`) - Unzips files and organizes them by type (XML/PDF)
- **Extraction Service** (`services/extraction_service/`) - Parses XML files and extracts CUFE values
- **API Service** (`services/api_service/`) - Main FastAPI REST API with endpoints for triggering processes and querying data

## 🛠️ Tech Stack

- **Language**: Python 3.x
- **Framework**: FastAPI
- **Database**: PostgreSQL
- **ORM**: SQLAlchemy
- **Authentication**: JWT
- **Containerization**: Docker + Docker Compose
- **File Processing**: zipfile, xml.etree.ElementTree, lxml
- **Email**: imaplib, email

## 📁 Project Structure

```
mailresearcher/
├── services/
│   ├── mailbox_service/          # Mailbox Connection Microservice
│   ├── file_processing_service/  # File Processing Microservice
│   ├── extraction_service/       # Information Extraction Microservice
│   └── api_service/              # Main FastAPI REST API
├── shared/
│   ├── database/                 # Database models and configuration
│   ├── utils/                    # Shared utilities
│   └── schemas/                  # Pydantic schemas
├── config/                       # Configuration files
├── tests/                        # Test files
├── requirements/                 # Requirements files
├── docker-compose.yml
├── .env.example
└── README.md
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Python 3.8+
- PostgreSQL (if running locally)

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mailresearcher
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run with Docker Compose**
   ```bash
   docker-compose up --build
   ```

4. **Access the Application**
   - **Frontend Interface**: http://localhost:3000
   - **Main API**: http://localhost:8000
   - **API Documentation**: http://localhost:8000/docs
   - **Database**: localhost:5432

## 🌐 Using the Frontend

1. **Open the web interface**: http://localhost:3000
2. **Configure your email settings**:
   - Email Server: `imap.gmail.com` (for Gmail)
   - Port: `993` (SSL)
   - Email & Password: Your credentials
3. **Process emails**: Click "🚀 Process Emails"
4. **Monitor results**: View processing status and downloaded files

## 📋 API Endpoints

- `POST /process-emails-sync` - Process emails synchronously
- `POST /process-emails` - Trigger background email processing
- `POST /auth/login` - User authentication
- `GET /cufe/{cufe_id}` - Get CUFE information
- `GET /cufe/` - List all processed CUFEs
- `GET /download/{file_type}/{cufe_id}` - Download XML or PDF files

## 🔧 Development

### Local Development Setup

1. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements/base.txt
   ```

3. **Run individual services**
   ```bash
   # API Service
   cd services/api_service
   uvicorn main:app --reload --port 8000
   
   # Mailbox Service
   cd services/mailbox_service
   uvicorn main:app --reload --port 8001
   ```

## 🧪 Testing

```bash
pytest tests/
```

## 📝 Configuration

Key configuration options in `.env`:

- **Database**: PostgreSQL connection settings
- **Email**: IMAP/POP3 server configuration
- **JWT**: Authentication settings
- **File Storage**: Local or cloud storage paths

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
